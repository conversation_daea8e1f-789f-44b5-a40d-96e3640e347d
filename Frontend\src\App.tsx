import { useState } from 'react';
import { SideBar } from './components/SideBar/SideBar';
import { Header } from './components/Chat/Header';
import { ChatSection } from './components/Chat/ChatSection';
import { FooterChat } from './components/Chat/FooterChat';
import { AuthModal } from './components/Modals/AuthModal';
import { useAuth } from './hooks/useAuth';
import { useChatContext } from './context/ChatContext';
import { ToastContainer } from 'react-toastify';
import { AccountLogo } from './components/Chat/AccountLogo';
import { LoginButton } from './components/Chat/LoginButton';
import { LoadingAccount } from './components/Loaders/loadingAccount';
import type { User } from './types/interfaces';

function App() {
  const { user, loading: authLoading, handleLogin, handleLogout } = useAuth();
  const [emailStyle, setEmailStyle] = useState("formal");
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // Usar el contexto para obtener el estado y las funciones del chat
  const { messages, sendChatMessage, handleSendEmail, loading, sendingEmail } = useChatContext();

  // La función de envío de mensajes ahora verifica autenticación
  const handleSendMessage = (prompt: string) => {
    if (!user) {
      setIsAuthModalOpen(true);
      return;
    }
    sendChatMessage(prompt, emailStyle);
  };

  function loginWithGoogle() {
    handleLogin();
  }

  async function logoutSession() {
    try {
      await handleLogout();
      console.log("Logout exitoso. Redirigiendo...");
      window.location.href = "/";
    } catch (error) {
      console.error("Error al cerrar sesión:", error);
    }
  }

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <main className="h-screen flex gap-1 bg-bg">
      {/* Mobile Header - Only visible on screens < lg */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-white shadow-md px-4 py-3 flex justify-between items-center">
        <button
          onClick={toggleSidebar}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <div className="flex items-center">
          {authLoading ? (
            <LoadingAccount />
          ) : user ? (
            <AccountLogo user={user as User} logoutFunction={logoutSession} />
          ) : (
            <LoginButton loginFunction={loginWithGoogle} />
          )}
        </div>
      </div>

      {/* Sidebar - Hidden on mobile by default, visible on desktop */}
      <div className={`
        fixed lg:relative top-0 left-0 h-full z-40 transition-transform duration-300 ease-in-out
        ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <SideBar />
      </div>

      {/* Overlay for mobile sidebar */}
      {isSidebarOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={closeSidebar}
        />
      )}

      {/* Main content */}
      <section className="h-full w-full p-5 pt-20 lg:pt-5">
        <div className="h-full w-full grid grid-rows-[auto_1fr_auto] bg-white rounded-4xl p-7 shadow-lg overflow-hidden">
          <div className="hidden lg:block">
            <Header onStyleChange={setEmailStyle} />
          </div>

          <div className="overflow-y-auto min-h-0 flex-1">
            <ChatSection
              messages={messages}
              onSendEmail={(draftContent) => handleSendEmail(draftContent, user?.email || '')}
              loading={loading}
              sendingEmail={sendingEmail}
            />
          </div>

          <FooterChat
            sendChatMessage={handleSendMessage}
            userId={user?.id || ''}
            isDisabled={sendingEmail}
          />
        </div>
      </section>

      {/* Modal de autenticación */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />

      <ToastContainer />
    </main>
  );
}

export default App;
